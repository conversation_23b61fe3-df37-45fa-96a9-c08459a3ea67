"lang"
{
	"Language"		"schinese"
	"Tokens"
	{
		"ror_translationCredit"	"中文翻译由 ModdedMarionette 提供。"
		"ror_translationVersion"	"翻译版本：v1.2"

		"rd_challenge_desc_ror"	"雨中冒险挑战 - 物品不丢失版！即使任务失败/任务重启，背包里的物品也不会丢失，除非手动重置。如何手动重置：游戏中聊天发送：/restart。其他相关命令：/i /ammo /med。原始 mod：https://steamcommunity.com/sharedfiles/filedetails/?id=2985633068"

		"ror_mapOnCooldown"	"此地图当前处于冷却中。请选择另一个地图！"

		"ror_howToInv"	"%s1输入 %s2 来检查你正在操作或观察的陆战队员的背包。"

		"ror_itemPickup"	"%s1你拾取了 %s2：%s3"

		"ror_itemPickupOther"	"%s1 拾取了 %s2：%s3"

		"ror_newItemCount"	"%s1你现在有 %s2 个 %s3。"

		"ror_newItemCount_1"	"%s1你现在有 %s2 个 %s3。"

		"ror_bossKeyDropped"	"Boss 钥匙已掉落。携带它到关卡末尾以在下一关卡生成 Boss。"

		"ror_bossKilled"	"Boss 已被击败。完成当前关卡以胜利结束冒险。"

		"ror_everyInvListed"	"所有陆战队员的背包已在上方列出。查看控制台获取完整视图。"

		"ror_progressReport_gameOver"	"%s1游戏结束：在%s4难度下于 %s3 完成了 %s2 个关卡。"

		"ror_progressReport_gameOver_1"	"%s1游戏结束：在%s4难度下于 %s3 完成了 %s2 个关卡。"

		"ror_progressReport_victory"	"%s1胜利：在%s4难度下于 %s3 完成了 %s2 个关卡。"

		"ror_progressReport_victory_1"	"%s1胜利：在%s4难度下于 %s3 完成了 %s2 个关卡。"

		"ror_progressReport_load"	"已加载保存进度：%s3 关卡 %s1，%s2 难度。"

		"ror_progressReport_save"	"已保存进度：%s3 关卡 %s1，%s2 难度。"

		"ror_totalKills"	"%s1此次冒险总共击杀 %s2 个敌人。"

		"ror_diffNotMatch"	"当前难度与保存进度不匹配。正在更改难度..."

		"ror_loadedInv"	"已加载 %s1 的背包。"

		"ror_deleteInv"	"%s1 处于非活跃状态。正在删除 %s1 的背包..."

		"ror_currentCarnage"	"%s1当前屠杀等级：%s2"

		"ror_outlierAssist"	"%s1异常值协助：你已获得 %s2 个随机物品以追赶团队的进度。"

		"ror_outlierAssistOther"	"%s1异常值协助：%s2 已获得 %s3 个随机物品以追赶团队的进度。"

		"ror_bossSpawned"	"%s1Boss 已在此关卡生成。击败 Boss 后完成关卡将以胜利结束冒险。未击败 Boss 完成关卡将继续冒险。"

		"ror_bossSpawnFail"	"Boss 生成失败。已排队在下一关卡生成 Boss。"

		"ror_bossWillSpawn"	"下一关卡将生成 Boss。"

		"ror_invDivider"	"%s1===== %s2 的背包 ====="

		"ror_noItems"	"=== 没有物品可检查。 ==="

		"ror_invItem"	"%s1：%s2"

		"ror_seeConsole"	"查看控制台获取完整视图。"

		"ror_queenObj"	"消灭所有女王"

		"ror_queenObjDesc"	"[可选] Boss 钥匙是在下一关卡生成 Boss 所必需的。消灭所有女王以获得 Boss 钥匙。"

		"ror_keyObj"	"护送 Boss 钥匙"

		"ror_keyObjDesc"	"[可选] 携带 Boss 钥匙到关卡末尾以在下一关卡生成 Boss。"

		"ror_bossObj"	"击败 Boss"

		"ror_bossObjDesc"	"[可选] 击败 Boss 以胜利结束冒险。"

		"ror_itemDesc_white_medkit"	"击杀敌人有概率掉落治疗包。叠加概率。"

		"ror_itemDesc_white_welder"	"在黑客过程中，所有附近队友获得 +50% 伤害和 +50% 抗性。叠加范围。"

		"ror_itemDesc_white_flares"	"攻击时有概率点燃敌人。叠加概率。"

		"ror_itemDesc_white_laser_mines"	"爆炸承受的伤害一次。脱离危险时充能。叠加范围。"

		"ror_itemDesc_white_normal_armor"	"+10% 抗性。叠加该效果。"

		"ror_itemDesc_white_buff_grenade"	"对血量高于 90% 的敌人增加伤害。叠加伤害。"

		"ror_itemDesc_white_hornet_barrage"	"首次使用按钮时发射火箭。叠加火箭数量。"

		"ror_itemDesc_white_freeze_grenades"	"攻击时有概率冰冻敌人。叠加概率。"

		"ror_itemDesc_white_stim"	"受到伤害后，在 2 秒内未被击中时治疗。叠加治疗量。"

		"ror_itemDesc_white_tesla_trap"	"攻击时有概率眩晕敌人。叠加概率。"

		"ror_itemDesc_white_electrified_armor"	"抵抗伤害一次。脱离危险时充能。叠加抗性。"

		"ror_itemDesc_white_mines"	"击杀敌人时点燃附近敌人。叠加范围。"

		"ror_itemDesc_white_fist"	"近距离增加伤害。叠加伤害。"

		"ror_itemDesc_white_grenades"	"攻击时有概率生成手榴弹。叠加概率。"

		"ror_itemDesc_white_night_vision"	"+10% 暴击概率。叠加该效果。"

		"ror_itemDesc_white_smart_bomb"	"关卡开始时，装备开始时有额外充能。叠加额外充能数量。"

		"ror_itemDesc_white_gas_grenades"	"脱离危险时治疗。叠加治疗量。"

		"ror_itemDesc_white_blink"	"受到攻击时有概率无效化承受的伤害。叠加概率。"

		"ror_itemDesc_white_jump_jet"	"对大型异形增加伤害。叠加伤害。"

		"ror_itemDesc_green_medkit"	"+5% 暴击概率。暴击时治疗。叠加治疗量。"

		"ror_itemDesc_green_welder"	"击杀敌人有概率掉落机枪哨兵。叠加概率。"

		"ror_itemDesc_green_flares"	"对燃烧中的敌人增加伤害。叠加伤害。"

		"ror_itemDesc_green_laser_mines"	"瞬间击杀血量低于一定数值的精英敌人。叠加阈值。"

		"ror_itemDesc_green_normal_armor"	"1 秒内击杀 4 个敌人提供 +50% 抗性和 +80% 伤害。叠加效果持续时间。"

		"ror_itemDesc_green_buff_grenade"	"+5% 暴击概率。暴击继承 80% 的伤害给目标附近的敌人。叠加受影响的敌人数量。"

		"ror_itemDesc_green_hornet_barrage"	"攻击时 10% 概率发射火箭。叠加火箭伤害。"

		"ror_itemDesc_green_freeze_grenades"	"被感染时冰冻所有附近敌人。叠加范围。"

		"ror_itemDesc_green_stim"	"攻击时 50% 概率治疗。叠加治疗量。"

		"ror_itemDesc_green_tesla_trap"	"攻击时 10% 概率眩晕目标附近的所有敌人。叠加范围。"

		"ror_itemDesc_green_electrified_armor"	"受到攻击时对附近敌人造成伤害。叠加范围和受影响的敌人数量。"

		"ror_itemDesc_green_mines"	"血量低于 25% 时生成高伤害燃烧手榴弹。叠加冷却缩减。"

		"ror_itemDesc_green_fist"	"吸取附近敌人的血量。叠加受影响的敌人数量。"

		"ror_itemDesc_green_grenades"	"击杀敌人时爆炸。叠加范围和伤害。"

		"ror_itemDesc_green_night_vision"	"对同时被眩晕和燃烧的敌人造成伤害时标记死亡。被标记的敌人受到 +50% 总伤害。叠加效果持续时间。"

		"ror_itemDesc_green_smart_bomb"	"击杀敌人有概率掉落弹药。叠加概率。"

		"ror_itemDesc_green_gas_grenades"	"在黑客过程中治疗所有附近队友。叠加治疗量。"

		"ror_itemDesc_green_blink"	"血量低于 25% 时冰冻所有附近敌人。叠加范围和冷却缩减。"

		"ror_itemDesc_green_jump_jet"	"脱离危险时增加伤害。叠加伤害。"

		"ror_itemDesc_red_medkit"	"血量低于 25% 时治疗最大血量的 75%。叠加冷却缩减。"

		"ror_itemDesc_red_welder"	"攻击时有概率钩住目标附近的敌人。叠加概率和受影响的敌人数量。"

		"ror_itemDesc_red_flares"	"在黑客过程中点燃和眩晕所有附近敌人。叠加范围。"

		"ror_itemDesc_red_laser_mines"	"攻击时有概率在小范围内造成大伤害。叠加伤害。"

		"ror_itemDesc_red_normal_armor"	"精英击杀获得 +80% 抗性和 +100% 暴击概率。叠加效果持续时间。"

		"ror_itemDesc_red_buff_grenade"	"增加 100% 暴击伤害。叠加该效果。"

		"ror_itemDesc_red_hornet_barrage"	"击杀敌人生成火箭。叠加火箭伤害。"

		"ror_itemDesc_red_freeze_grenades"	"附近冰冻的敌人受到伤害。叠加范围。"

		"ror_itemDesc_red_stim"	"击杀敌人有概率掉落治疗信标。叠加信标范围。"

		"ror_itemDesc_red_tesla_trap"	"每秒眩晕附近敌人。叠加受影响的敌人数量。"

		"ror_itemDesc_red_electrified_armor"	"受到伤害时激活电力护甲 5 秒。叠加冷却缩减。"

		"ror_itemDesc_red_mines"	"+5% 暴击概率。暴击时燃烧敌人。燃烧敌人死亡时爆炸。叠加伤害。"

		"ror_itemDesc_red_fist"	"所有攻击爆炸，造成 60% 总伤害。叠加范围。"

		"ror_itemDesc_red_grenades"	"7 秒内击杀 4 个敌人向最近敌人发射高伤害手榴弹。叠加伤害。"

		"ror_itemDesc_red_night_vision"	"击杀敌人有概率复活为幽灵盟友。叠加幽灵寿命。"

		"ror_itemDesc_red_smart_bomb"	"所有物品生成的火箭额外生成 2 次。叠加火箭额外伤害。"

		"ror_itemDesc_red_gas_grenades"	"每秒治疗。血量低于 50% 时立即治愈感染。叠加治疗量。"

		"ror_itemDesc_red_blink"	"所有随机效果重新投掷以获得有利结果。叠加重投次数。"

		"ror_itemDesc_red_jump_jet"	"攻击敌人永久增加该敌人下次受到的伤害。叠加增加伤害。"

		"ror_command_restart_desc"	"输入 /restart 重置冒险并删除存档。"

		"ror_command_ammo_desc"	"输入 /ammo 获得 1 个击杀概率掉落弹药。"

		"ror_command_med_desc"	"输入 /med 获得 1 个击杀概率掉落医疗包。"

		"ror_restart_success"	"游戏进度已重置。正在返回大厅..."

		"ror_ammo_added"	"%s1添加了 %s2：%s3"

		"ror_med_added"	"%s1添加了 %s2：%s3"

		"ror_no_marine_error"	"你必须控制一个陆战队员才能使用此命令。"
	}
}
